package service

import (
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/repository"
	ws "magnet-downloader/internal/websocket"
	"magnet-downloader/pkg/database"
	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/javscraper"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/queue"
	"magnet-downloader/pkg/streaming"
	"magnet-downloader/pkg/streamhg"
	"magnet-downloader/pkg/streamtape"
)

// Services 服务集合
type Services struct {
	Task           TaskService
	User           UserService
	Config         ConfigService
	Download       DownloadService
	Queue          QueueService
	Aria2          Aria2Service
	WebSocket      *ws.Service
	FileProcessing FileProcessingService
	AutoUpload     AutoUploadService
	Telegram       TelegramService
	JAV            JAVService
	JAVScraper     JAVScraperService
	JAVDownload    JAVDownloadService
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	JWTSecret string
}

// NewServices 创建服务集合
func NewServices(repo repository.Repository, cfg *config.Config) *Services {
	// 创建队列
	redisQueue := queue.NewRedisQueue("magnet-downloader")

	// 创建基础服务
	queueService := NewQueueService(redisQueue)
	aria2Service := NewAria2Service(&cfg.Aria2)
	configService := NewConfigService(repo)
	userService := NewUserService(repo, cfg.JWT.Secret)
	downloadService := NewDownloadService(repo, aria2Service)
	taskService := NewTaskService(repo, downloadService, queueService)
	webSocketService := ws.NewService(repo.GetDB())

	// 创建文件处理服务
	processorConfig := &fileprocessor.ProcessingConfig{
		ChunkSizeMB:       cfg.FileProcessing.ChunkSizeMB,
		EncryptionEnabled: cfg.FileProcessing.EncryptionEnabled,
		KeepOriginal:      cfg.FileProcessing.KeepOriginalFiles,
		WorkDir:           cfg.FileProcessing.WorkDir,
	}

	imgbbConfig := &imgbb.Config{
		APIKey:     cfg.FileProcessing.ImgBB.APIKey,
		BaseURL:    cfg.FileProcessing.ImgBB.BaseURL,
		Timeout:    time.Duration(cfg.FileProcessing.ImgBB.Timeout) * time.Second,
		MaxRetries: cfg.FileProcessing.ImgBB.MaxRetries,
	}

	playlistConfig := &streaming.PlaylistConfig{
		Version:        cfg.FileProcessing.Playlist.Version,
		TargetDuration: cfg.FileProcessing.Playlist.TargetDuration,
		MediaSequence:  cfg.FileProcessing.Playlist.MediaSequence,
		AllowCache:     cfg.FileProcessing.Playlist.AllowCache,
		PlaylistType:   cfg.FileProcessing.Playlist.PlaylistType,
	}

	// MixFile配置
	mixFileConfig := &config.MixFileConfig{
		Enabled:                cfg.FileProcessing.MixFile.Enabled,
		EnableSteganography:    cfg.FileProcessing.MixFile.EnableSteganography,
		EnableIndexCompression: cfg.FileProcessing.MixFile.EnableIndexCompression,
		EnableIndexEncryption:  cfg.FileProcessing.MixFile.EnableIndexEncryption,
		ShareCodePrefix:        cfg.FileProcessing.MixFile.ShareCodePrefix,
		MaxShareCodeLength:     cfg.FileProcessing.MixFile.MaxShareCodeLength,
		IndexUploadRetries:     cfg.FileProcessing.MixFile.IndexUploadRetries,
	}

	// 创建StreamTape配置
	streamTapeConfig := &streamtape.Config{
		APILogin:   cfg.FileProcessing.StreamTape.APILogin,
		APIKey:     cfg.FileProcessing.StreamTape.APIKey,
		BaseURL:    cfg.FileProcessing.StreamTape.BaseURL,
		Timeout:    time.Duration(cfg.FileProcessing.StreamTape.Timeout) * time.Second,
		MaxRetries: cfg.FileProcessing.StreamTape.MaxRetries,
	}

	fileProcessingService := NewFileProcessingService(
		database.GetDB(),
		webSocketService,
		processorConfig,
		imgbbConfig,
		streamTapeConfig,
		cfg.FileProcessing.UploadProvider,
		playlistConfig,
		mixFileConfig,
	)

	// 设置下载服务的文件处理服务引用
	downloadService.SetFileProcessingService(fileProcessingService)

	// 创建Telegram通知服务
	telegramService := NewTelegramService(&cfg.Telegram)

	// 创建自动上传服务
	var autoUploadService AutoUploadService
	if cfg.FileProcessing.AutoUpload.Enabled {
		// 创建StreamTape客户端
		var streamTapeClient *streamtape.Client
		var streamHGClient *streamhg.Client

		// 根据上传提供商创建相应的客户端
		switch cfg.FileProcessing.UploadProvider {
		case "streamtape":
			streamTapeClient = streamtape.NewClient(streamTapeConfig)
			logger.Infof("自动上传服务: 创建StreamTape客户端")
		case "streamhg":
			// 创建StreamHG配置
			streamHGConfig := &streamhg.Config{
				APIKey:     cfg.FileProcessing.StreamHG.APIKey,
				BaseURL:    cfg.FileProcessing.StreamHG.BaseURL,
				Timeout:    time.Duration(cfg.FileProcessing.StreamHG.Timeout) * time.Second,
				MaxRetries: cfg.FileProcessing.StreamHG.MaxRetries,
			}
			streamHGClient = streamhg.NewClient(streamHGConfig)
			logger.Infof("自动上传服务: 创建StreamHG客户端")
		case "dual":
			// dual模式：同时创建两个客户端
			logger.Infof("自动上传服务: 启用dual模式，创建StreamTape和StreamHG客户端")
			streamTapeClient = streamtape.NewClient(streamTapeConfig)
			streamHGConfig := &streamhg.Config{
				APIKey:     cfg.FileProcessing.StreamHG.APIKey,
				BaseURL:    cfg.FileProcessing.StreamHG.BaseURL,
				Timeout:    time.Duration(cfg.FileProcessing.StreamHG.Timeout) * time.Second,
				MaxRetries: cfg.FileProcessing.StreamHG.MaxRetries,
			}
			streamHGClient = streamhg.NewClient(streamHGConfig)
			logger.Infof("自动上传服务: StreamTape客户端=%v, StreamHG客户端=%v", streamTapeClient != nil, streamHGClient != nil)
		case "triple":
			// triple模式：创建三个客户端（StreamTape、StreamHG、VOE）
			logger.Infof("自动上传服务: 启用triple模式，创建StreamTape、StreamHG和VOE客户端")
			streamTapeClient = streamtape.NewClient(streamTapeConfig)
			streamHGConfig := &streamhg.Config{
				APIKey:     cfg.FileProcessing.StreamHG.APIKey,
				BaseURL:    cfg.FileProcessing.StreamHG.BaseURL,
				Timeout:    time.Duration(cfg.FileProcessing.StreamHG.Timeout) * time.Second,
				MaxRetries: cfg.FileProcessing.StreamHG.MaxRetries,
			}
			streamHGClient = streamhg.NewClient(streamHGConfig)
			logger.Infof("自动上传服务: StreamTape客户端=%v, StreamHG客户端=%v, VOE配置已加载", 
				streamTapeClient != nil, streamHGClient != nil)
		}

		autoUploadService = NewAutoUploadService(
			database.GetDB(),
			webSocketService,
			streamTapeClient,
			streamHGClient,
			telegramService,
			cfg,
		)
	}

	// 创建JAV服务
	javService := NewJAVService(repo)
	
	// 创建JAV爬虫配置
	javScraperConfig := &javscraper.Config{
		Timeout:    30 * time.Second,
		RateLimit:  1 * time.Second,
		MaxRetries: 3,
	}
	
	javScraperService := NewJAVScraperService(repo, javScraperConfig)
	javDownloadService := NewJAVDownloadService(repo, downloadService, autoUploadService)

	return &Services{
		Task:           taskService,
		User:           userService,
		Config:         configService,
		Download:       downloadService,
		Queue:          queueService,
		Aria2:          aria2Service,
		WebSocket:      webSocketService,
		FileProcessing: fileProcessingService,
		AutoUpload:     autoUploadService,
		Telegram:       telegramService,
		JAV:            javService,
		JAVScraper:     javScraperService,
		JAVDownload:    javDownloadService,
	}
}

// Initialize 初始化服务
func (s *Services) Initialize() error {
	// 连接aria2
	if err := s.Aria2.Connect(); err != nil {
		// aria2连接失败不应该阻止服务启动
		// logger.Warnf("Failed to connect to aria2: %v", err)
	}

	// 刷新配置缓存
	if err := s.Config.RefreshConfigCache(); err != nil {
		return err
	}

	return nil
}

// Close 关闭服务
func (s *Services) Close() error {
	// 断开aria2连接
	if err := s.Aria2.Disconnect(); err != nil {
		// logger.Warnf("Failed to disconnect from aria2: %v", err)
	}

	return nil
}

// HealthCheck 健康检查
func (s *Services) HealthCheck() map[string]bool {
	health := make(map[string]bool)

	// 检查aria2连接
	health["aria2"] = s.Aria2.Ping() == nil

	// 检查队列服务
	health["queue"] = s.Queue.HealthCheck() == nil

	return health
}
