package javinizer

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"magnet-downloader/pkg/javscraper/client"
	"magnet-downloader/pkg/logger"
)

// Javinizer包装器API响应结构体
type WrapperAPIResponse struct {
	Success bool                   `json:"success"`
	Data    *WrapperMovieInfo      `json:"data"`
	Code    string                 `json:"code"`
	Source  string                 `json:"source"`
	Error   string                 `json:"error,omitempty"`
}

type WrapperMovieInfo struct {
	Code        string   `json:"code"`
	Title       string   `json:"title"`
	Plot        string   `json:"plot"`
	Cover       string   `json:"cover"`
	BigCover    string   `json:"big_cover"`
	Actress     []string `json:"actress"`
	Genre       []string `json:"genre"`
	Director    string   `json:"director"`
	Duration    string   `json:"duration"`
	Producer    string   `json:"producer"`
	Publisher   string   `json:"publisher"`
	ReleaseDate string   `json:"release_date"`
	Serial      string   `json:"serial"`
	Score       string   `json:"score"`
	URL         string   `json:"url"`
	Source      string   `json:"source"`
	ScrapedAt   string   `json:"scraped_at"`
}

// Adapter Javinizer数据源适配器
type Adapter struct {
	baseClient     *client.BaseClient
	config         *Config
	enabled        bool
	sources        map[string]SourceHandler
	wrapperBaseURL string
}

// SourceHandler 数据源处理器接口
type SourceHandler interface {
	GetMovieByCode(code string) (*MovieInfo, error)
	GetSourceName() string
	IsEnabled() bool
}

// NewAdapter 创建新的Javinizer适配器
func NewAdapter(config *Config, userAgent string, wrapperBaseURL ...string) *Adapter {
	if config == nil {
		config = &Config{
			Enabled:   true,
			Sources:   []string{"javlibrary", "r18", "dmm"},
			Timeout:   30 * time.Second,
			RateLimit: 2 * time.Second,
		}
	}

	baseClient := client.NewBaseClient(
		userAgent,
		config.Timeout,
		3, // maxRetries
		config.RateLimit,
	)

	// 设置包装器URL
	defaultWrapperURL := "http://localhost:3003"
	if len(wrapperBaseURL) > 0 && wrapperBaseURL[0] != "" {
		defaultWrapperURL = wrapperBaseURL[0]
	}

	adapter := &Adapter{
		baseClient:     baseClient,
		config:         config,
		enabled:        config.Enabled,
		sources:        make(map[string]SourceHandler),
		wrapperBaseURL: defaultWrapperURL,
	}

	// 初始化数据源处理器
	adapter.initSourceHandlers()

	return adapter
}

// getMovieFromWrapper 从Javinizer包装器获取影片信息
func (a *Adapter) getMovieFromWrapper(code string) (*MovieInfo, error) {
	// 构建API URL
	apiURL := fmt.Sprintf("%s/api/movie/%s", a.wrapperBaseURL, code)
	
	ctx, cancel := context.WithTimeout(context.Background(), a.config.Timeout)
	defer cancel()

	// 发送HTTP请求
	response, err := a.baseClient.Get(ctx, apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("Javinizer包装器API请求失败: %v", err)
	}

	// 解析API响应
	var apiResp WrapperAPIResponse
	if err := json.Unmarshal(response, &apiResp); err != nil {
		return nil, fmt.Errorf("解析Javinizer包装器API响应失败: %v", err)
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("Javinizer包装器API返回错误: %s", apiResp.Error)
	}

	if apiResp.Data == nil {
		return nil, fmt.Errorf("Javinizer包装器API返回空数据")
	}

	// 转换API数据为MovieInfo
	movieInfo := a.convertWrapperDataToMovieInfo(apiResp.Data)
	return movieInfo, nil
}

// convertWrapperDataToMovieInfo 将包装器API数据转换为MovieInfo
func (a *Adapter) convertWrapperDataToMovieInfo(data *WrapperMovieInfo) *MovieInfo {
	movieInfo := &MovieInfo{
		Code:     data.Code,
		Title:    data.Title,
		Plot:     data.Plot,
		CoverURL: data.Cover,
		Studio:   data.Producer,
		Series:   data.Serial,
		Director: data.Director,
		Source:   "javinizer-wrapper",
		ScrapedAt: time.Now(),
		Confidence: 0.85, // Javinizer包装器的基础可信度
	}

	// 解析发布日期
	if data.ReleaseDate != "" {
		if releaseDate, err := time.Parse("2006-01-02", data.ReleaseDate); err == nil {
			movieInfo.ReleaseDate = releaseDate
		}
	}

	// 解析时长
	if data.Duration != "" {
		if duration, err := time.ParseDuration(data.Duration + "m"); err == nil {
			movieInfo.Duration = int(duration.Minutes())
		}
	}

	// 转换演员信息
	for _, actress := range data.Actress {
		movieInfo.Actors = append(movieInfo.Actors, ActorInfo{
			Name: actress,
		})
	}

	// 转换分类信息
	for _, genre := range data.Genre {
		movieInfo.Genres = append(movieInfo.Genres, GenreInfo{
			Name: genre,
		})
	}

	return movieInfo
}

// checkWrapperHealth 检查包装器服务健康状态
func (a *Adapter) checkWrapperHealth() error {
	healthURL := fmt.Sprintf("%s/health", a.wrapperBaseURL)
	
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := a.baseClient.Get(ctx, healthURL, nil)
	if err != nil {
		return fmt.Errorf("Javinizer包装器服务不可用: %v", err)
	}
	
	return nil
}

// initSourceHandlers 初始化数据源处理器
func (a *Adapter) initSourceHandlers() {
	for _, sourceName := range a.config.Sources {
		switch sourceName {
		case "javlibrary":
			a.sources[sourceName] = NewJavLibraryHandler(a.baseClient)
		case "r18":
			a.sources[sourceName] = NewR18Handler(a.baseClient)
		case "dmm":
			a.sources[sourceName] = NewDMMHandler(a.baseClient)
		default:
			logger.Warnf("未知的Javinizer数据源: %s", sourceName)
		}
	}
}

// GetMovieByCode 根据番号获取影片信息
func (a *Adapter) GetMovieByCode(code string) (*ScrapingResult, error) {
	if !a.enabled {
		return &ScrapingResult{
			Success: false,
			Error:   "Javinizer适配器未启用",
			Source:  "javinizer",
		}, nil
	}

	startTime := time.Now()

	// 首先尝试调用Javinizer包装器API
	movieInfo, err := a.getMovieFromWrapper(code)
	if err == nil && movieInfo != nil {
		return &ScrapingResult{
			Success:   true,
			MovieInfo: movieInfo,
			Source:    "javinizer",
			Duration:  time.Since(startTime),
		}, nil
	}

	logger.Warnf("Javinizer包装器调用失败，回退到传统数据源: %v", err)

	// 回退到传统的多数据源方式
	var lastErr error
	var bestResult *MovieInfo

	// 尝试从各个数据源获取信息
	for sourceName, handler := range a.sources {
		if !handler.IsEnabled() {
			continue
		}

		logger.Debugf("从%s获取影片信息: %s", sourceName, code)
		
		movieInfo, err := handler.GetMovieByCode(code)
		if err != nil {
			logger.Warnf("从%s获取影片信息失败: %v", sourceName, err)
			lastErr = err
			continue
		}

		if movieInfo != nil {
			// 设置数据源信息
			movieInfo.Source = fmt.Sprintf("javinizer-%s", sourceName)
			movieInfo.ScrapedAt = time.Now()
			movieInfo.Confidence = a.calculateConfidence(sourceName, movieInfo)

			// 选择最佳结果（优先级：r18 > dmm > javlibrary）
			if bestResult == nil || a.isResultBetter(movieInfo, bestResult) {
				bestResult = movieInfo
			}
		}
	}

	if bestResult == nil {
		return &ScrapingResult{
			Success:  false,
			Error:    fmt.Sprintf("所有Javinizer数据源都无法获取影片信息: %v", lastErr),
			Source:   "javinizer",
			Duration: time.Since(startTime),
		}, nil
	}

	return &ScrapingResult{
		Success:   true,
		MovieInfo: bestResult,
		Source:    "javinizer",
		Duration:  time.Since(startTime),
	}, nil
}

// calculateConfidence 计算数据可信度
func (a *Adapter) calculateConfidence(sourceName string, movieInfo *MovieInfo) float64 {
	baseConfidence := map[string]float64{
		"r18":        0.9,
		"dmm":        0.8,
		"javlibrary": 0.7,
	}

	confidence := baseConfidence[sourceName]
	if confidence == 0 {
		confidence = 0.6 // 默认可信度
	}

	// 根据数据完整性调整可信度
	if movieInfo.TitleEn != "" {
		confidence += 0.05
	}
	if movieInfo.PlotEn != "" {
		confidence += 0.05
	}
	if len(movieInfo.Actors) > 0 {
		confidence += 0.05
	}
	if movieInfo.Rating > 0 {
		confidence += 0.05
	}

	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// isResultBetter 判断结果是否更好
func (a *Adapter) isResultBetter(newResult, currentResult *MovieInfo) bool {
	// 优先级顺序：r18 > dmm > javlibrary
	sourcePriority := map[string]int{
		"javinizer-r18":        1,
		"javinizer-dmm":        2,
		"javinizer-javlibrary": 3,
	}

	newPriority := sourcePriority[newResult.Source]
	currentPriority := sourcePriority[currentResult.Source]

	if newPriority == 0 {
		newPriority = 999
	}
	if currentPriority == 0 {
		currentPriority = 999
	}

	return newPriority < currentPriority
}

// SearchMovies 搜索影片
func (a *Adapter) SearchMovies(keyword string, page int, pageSize int) (*SearchResult, error) {
	if !a.enabled {
		return &SearchResult{
			Movies: []MovieInfo{},
			Total:  0,
			Source: "javinizer",
		}, nil
	}

	// Javinizer主要用于补充英文信息，搜索功能暂不实现
	return &SearchResult{
		Movies:   []MovieInfo{},
		Total:    0,
		Page:     page,
		PageSize: pageSize,
		HasMore:  false,
		Source:   "javinizer",
	}, nil
}

// GetLatestMovies 获取最新影片
func (a *Adapter) GetLatestMovies(page int, pageSize int) (*SearchResult, error) {
	if !a.enabled {
		return &SearchResult{
			Movies: []MovieInfo{},
			Total:  0,
			Source: "javinizer",
		}, nil
	}

	// Javinizer主要用于补充英文信息，最新影片功能暂不实现
	return &SearchResult{
		Movies:   []MovieInfo{},
		Total:    0,
		Page:     page,
		PageSize: pageSize,
		HasMore:  false,
		Source:   "javinizer",
	}, nil
}

// GetActorInfo 获取演员信息
func (a *Adapter) GetActorInfo(actorName string) (*ActorInfo, error) {
	if !a.enabled {
		return nil, fmt.Errorf("Javinizer适配器未启用")
	}

	// 尝试从各个数据源获取演员信息
	for sourceName, handler := range a.sources {
		if !handler.IsEnabled() {
			continue
		}

		// 这里可以扩展演员信息获取功能
		logger.Debugf("从%s获取演员信息: %s", sourceName, actorName)
	}

	// 暂时返回基本信息
	return &ActorInfo{
		Name: actorName,
	}, nil
}

// GetSourceName 获取数据源名称
func (a *Adapter) GetSourceName() string {
	return "javinizer"
}

// IsEnabled 检查是否启用
func (a *Adapter) IsEnabled() bool {
	return a.enabled
}

// Close 关闭适配器
func (a *Adapter) Close() error {
	if a.baseClient != nil {
		return a.baseClient.Close()
	}
	return nil
}

// JavLibraryHandler JavLibrary处理器
type JavLibraryHandler struct {
	baseClient *client.BaseClient
}

func NewJavLibraryHandler(baseClient *client.BaseClient) *JavLibraryHandler {
	return &JavLibraryHandler{
		baseClient: baseClient,
	}
}

func (h *JavLibraryHandler) GetMovieByCode(code string) (*MovieInfo, error) {
	// 实现JavLibrary数据获取逻辑
	// 这里是简化实现，实际需要根据JavLibrary的页面结构来解析
	return &MovieInfo{
		Code:   code,
		Source: "javinizer-javlibrary",
	}, nil
}

func (h *JavLibraryHandler) GetSourceName() string {
	return "javlibrary"
}

func (h *JavLibraryHandler) IsEnabled() bool {
	return true
}

// R18Handler R18处理器
type R18Handler struct {
	baseClient *client.BaseClient
}

func NewR18Handler(baseClient *client.BaseClient) *R18Handler {
	return &R18Handler{
		baseClient: baseClient,
	}
}

func (h *R18Handler) GetMovieByCode(code string) (*MovieInfo, error) {
	// 实现R18数据获取逻辑
	// R18主要提供英文标题和描述
	return &MovieInfo{
		Code:    code,
		TitleEn: fmt.Sprintf("English Title for %s", code), // 示例
		PlotEn:  fmt.Sprintf("English plot for %s", code),  // 示例
		Source:  "javinizer-r18",
	}, nil
}

func (h *R18Handler) GetSourceName() string {
	return "r18"
}

func (h *R18Handler) IsEnabled() bool {
	return true
}

// DMMHandler DMM处理器
type DMMHandler struct {
	baseClient *client.BaseClient
}

func NewDMMHandler(baseClient *client.BaseClient) *DMMHandler {
	return &DMMHandler{
		baseClient: baseClient,
	}
}

func (h *DMMHandler) GetMovieByCode(code string) (*MovieInfo, error) {
	// 实现DMM数据获取逻辑
	// DMM主要提供官方信息和高质量图片
	return &MovieInfo{
		Code:   code,
		Source: "javinizer-dmm",
	}, nil
}

func (h *DMMHandler) GetSourceName() string {
	return "dmm"
}

func (h *DMMHandler) IsEnabled() bool {
	return true
}